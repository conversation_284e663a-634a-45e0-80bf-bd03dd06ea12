"""
Enterprise Exception Hierarchy for AI/ML Platform
================================================

Comprehensive exception handling system with proper error codes,
logging integration, and enterprise-grade error reporting.

Author: Enterprise AI/ML Platform Team
Version: 2.0.0
"""

import logging
import traceback
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel


class ErrorSeverity(str, Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(str, Enum):
    """Error categories for classification."""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    VALIDATION = "validation"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_SERVICE = "external_service"
    INFRASTRUCTURE = "infrastructure"
    DATA_PROCESSING = "data_processing"
    MODEL_OPERATION = "model_operation"
    SYSTEM = "system"


class ErrorCode(str, Enum):
    """Standardized error codes."""
    # Authentication & Authorization
    AUTH_INVALID_CREDENTIALS = "AUTH_001"
    AUTH_TOKEN_EXPIRED = "AUTH_002"
    AUTH_TOKEN_INVALID = "AUTH_003"
    AUTH_INSUFFICIENT_PERMISSIONS = "AUTH_004"
    AUTH_ACCOUNT_LOCKED = "AUTH_005"
    AUTH_MFA_REQUIRED = "AUTH_006"
    
    # Validation
    VALIDATION_REQUIRED_FIELD = "VAL_001"
    VALIDATION_INVALID_FORMAT = "VAL_002"
    VALIDATION_OUT_OF_RANGE = "VAL_003"
    VALIDATION_DUPLICATE_VALUE = "VAL_004"
    VALIDATION_CONSTRAINT_VIOLATION = "VAL_005"
    
    # Business Logic
    BUSINESS_RESOURCE_NOT_FOUND = "BIZ_001"
    BUSINESS_RESOURCE_CONFLICT = "BIZ_002"
    BUSINESS_OPERATION_NOT_ALLOWED = "BIZ_003"
    BUSINESS_QUOTA_EXCEEDED = "BIZ_004"
    BUSINESS_DEPENDENCY_MISSING = "BIZ_005"
    
    # Model Operations
    MODEL_NOT_FOUND = "MDL_001"
    MODEL_TRAINING_FAILED = "MDL_002"
    MODEL_INFERENCE_FAILED = "MDL_003"
    MODEL_DEPLOYMENT_FAILED = "MDL_004"
    MODEL_VERSION_CONFLICT = "MDL_005"
    MODEL_RESOURCE_EXHAUSTED = "MDL_006"
    
    # Data Processing
    DATA_INVALID_FORMAT = "DAT_001"
    DATA_PROCESSING_FAILED = "DAT_002"
    DATA_CORRUPTION_DETECTED = "DAT_003"
    DATA_ACCESS_DENIED = "DAT_004"
    DATA_SIZE_LIMIT_EXCEEDED = "DAT_005"
    
    # Infrastructure
    INFRA_SERVICE_UNAVAILABLE = "INF_001"
    INFRA_TIMEOUT = "INF_002"
    INFRA_RESOURCE_EXHAUSTED = "INF_003"
    INFRA_CONFIGURATION_ERROR = "INF_004"
    INFRA_NETWORK_ERROR = "INF_005"
    
    # System
    SYSTEM_INTERNAL_ERROR = "SYS_001"
    SYSTEM_NOT_IMPLEMENTED = "SYS_002"
    SYSTEM_MAINTENANCE_MODE = "SYS_003"


class ErrorDetail(BaseModel):
    """Detailed error information."""
    field: Optional[str] = None
    message: str
    code: Optional[str] = None
    value: Optional[Any] = None


class ErrorResponse(BaseModel):
    """Standardized error response format."""
    error_id: str
    error_code: ErrorCode
    message: str
    details: List[ErrorDetail] = []
    category: ErrorCategory
    severity: ErrorSeverity
    timestamp: datetime
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    trace_id: Optional[str] = None


class EnterpriseException(Exception):
    """Base enterprise exception with comprehensive error handling."""
    
    def __init__(
        self,
        message: str,
        error_code: ErrorCode,
        category: ErrorCategory,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[List[ErrorDetail]] = None,
        user_id: Optional[str] = None,
        request_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        cause: Optional[Exception] = None
    ):
        self.error_id = str(uuid.uuid4())
        self.message = message
        self.error_code = error_code
        self.category = category
        self.severity = severity
        self.details = details or []
        self.user_id = user_id
        self.request_id = request_id
        self.trace_id = trace_id
        self.cause = cause
        self.timestamp = datetime.utcnow()
        
        super().__init__(self.message)
        self._log_exception()
    
    def _log_exception(self) -> None:
        """Log the exception with appropriate level."""
        logger = logging.getLogger(f"aiml.exceptions.{self.category.value}")
        
        log_data = {
            "error_id": self.error_id,
            "error_code": self.error_code.value,
            "message": self.message,
            "category": self.category.value,
            "severity": self.severity.value,
            "user_id": self.user_id,
            "request_id": self.request_id,
            "trace_id": self.trace_id,
            "timestamp": self.timestamp.isoformat(),
            "details": [detail.dict() for detail in self.details]
        }
        
        if self.cause:
            log_data["cause"] = str(self.cause)
            log_data["traceback"] = traceback.format_exception(
                type(self.cause), self.cause, self.cause.__traceback__
            )
        
        if self.severity == ErrorSeverity.CRITICAL:
            logger.critical("Critical error occurred", extra=log_data)
        elif self.severity == ErrorSeverity.HIGH:
            logger.error("High severity error occurred", extra=log_data)
        elif self.severity == ErrorSeverity.MEDIUM:
            logger.warning("Medium severity error occurred", extra=log_data)
        else:
            logger.info("Low severity error occurred", extra=log_data)
    
    def to_response(self) -> ErrorResponse:
        """Convert exception to error response."""
        return ErrorResponse(
            error_id=self.error_id,
            error_code=self.error_code,
            message=self.message,
            details=self.details,
            category=self.category,
            severity=self.severity,
            timestamp=self.timestamp,
            request_id=self.request_id,
            user_id=self.user_id,
            trace_id=self.trace_id
        )


# Specific Exception Classes
class AuthenticationException(EnterpriseException):
    """Authentication-related exceptions."""
    
    def __init__(self, message: str = "Authentication failed", **kwargs):
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTH_INVALID_CREDENTIALS,
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )


class AuthorizationException(EnterpriseException):
    """Authorization-related exceptions."""
    
    def __init__(self, message: str = "Access denied", **kwargs):
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTH_INSUFFICIENT_PERMISSIONS,
            category=ErrorCategory.AUTHORIZATION,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )


class ValidationException(EnterpriseException):
    """Validation-related exceptions."""
    
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        details = kwargs.pop('details', [])
        if field:
            details.append(ErrorDetail(field=field, message=message))
        
        super().__init__(
            message=message,
            error_code=ErrorCode.VALIDATION_INVALID_FORMAT,
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.MEDIUM,
            details=details,
            **kwargs
        )


class ModelException(EnterpriseException):
    """Model operation exceptions."""
    
    def __init__(self, message: str, model_name: Optional[str] = None, **kwargs):
        details = kwargs.pop('details', [])
        if model_name:
            details.append(ErrorDetail(field="model_name", message=f"Model: {model_name}"))
        
        super().__init__(
            message=message,
            error_code=ErrorCode.MODEL_NOT_FOUND,
            category=ErrorCategory.MODEL_OPERATION,
            severity=ErrorSeverity.HIGH,
            details=details,
            **kwargs
        )


class DataException(EnterpriseException):
    """Data processing exceptions."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            error_code=ErrorCode.DATA_PROCESSING_FAILED,
            category=ErrorCategory.DATA_PROCESSING,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )


class InfrastructureException(EnterpriseException):
    """Infrastructure-related exceptions."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            error_code=ErrorCode.INFRA_SERVICE_UNAVAILABLE,
            category=ErrorCategory.INFRASTRUCTURE,
            severity=ErrorSeverity.CRITICAL,
            **kwargs
        )


# Exception Handler Setup
def setup_enterprise_exception_handlers(app):
    """Setup comprehensive exception handlers."""
    
    @app.exception_handler(EnterpriseException)
    async def enterprise_exception_handler(request: Request, exc: EnterpriseException):
        """Handle enterprise exceptions."""
        error_response = exc.to_response()
        
        # Map severity to HTTP status codes
        status_map = {
            ErrorSeverity.LOW: status.HTTP_400_BAD_REQUEST,
            ErrorSeverity.MEDIUM: status.HTTP_400_BAD_REQUEST,
            ErrorSeverity.HIGH: status.HTTP_500_INTERNAL_SERVER_ERROR,
            ErrorSeverity.CRITICAL: status.HTTP_503_SERVICE_UNAVAILABLE
        }
        
        # Override for specific categories
        if exc.category == ErrorCategory.AUTHENTICATION:
            status_code = status.HTTP_401_UNAUTHORIZED
        elif exc.category == ErrorCategory.AUTHORIZATION:
            status_code = status.HTTP_403_FORBIDDEN
        elif exc.category == ErrorCategory.VALIDATION:
            status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        else:
            status_code = status_map.get(exc.severity, status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return JSONResponse(
            status_code=status_code,
            content=error_response.dict()
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle unexpected exceptions."""
        error_id = str(uuid.uuid4())
        
        # Log the unexpected exception
        logger = logging.getLogger("aiml.exceptions.system")
        logger.critical(
            "Unexpected exception occurred",
            extra={
                "error_id": error_id,
                "exception_type": type(exc).__name__,
                "message": str(exc),
                "traceback": traceback.format_exc(),
                "request_url": str(request.url),
                "request_method": request.method
            }
        )
        
        # Return generic error response
        error_response = ErrorResponse(
            error_id=error_id,
            error_code=ErrorCode.SYSTEM_INTERNAL_ERROR,
            message="An unexpected error occurred",
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.CRITICAL,
            timestamp=datetime.utcnow()
        )
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response.dict()
        )
