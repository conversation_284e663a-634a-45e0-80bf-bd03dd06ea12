"""
Enterprise Structured Logging System
===================================

Comprehensive logging system with JSON formatting, correlation IDs,
performance metrics, and enterprise-grade log management.

Author: Enterprise AI/ML Platform Team
Version: 2.0.0
"""

import json
import logging
import logging.config
import sys
import time
import uuid
from contextvars import ContextVar
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional, Union

from pythonjsonlogger import jsonlogger


# Context variables for request correlation
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
user_id_var: ContextVar[Optional[str]] = ContextVar('user_id', default=None)
trace_id_var: ContextVar[Optional[str]] = ContextVar('trace_id', default=None)


class LogLevel(str, Enum):
    """Log levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogCategory(str, Enum):
    """Log categories for classification."""
    APPLICATION = "application"
    SECURITY = "security"
    PERFORMANCE = "performance"
    BUSINESS = "business"
    INFRASTRUCTURE = "infrastructure"
    AUDIT = "audit"
    ML_OPERATIONS = "ml_operations"
    DATA_PROCESSING = "data_processing"


class EnterpriseJSONFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter with enterprise fields."""
    
    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]) -> None:
        """Add enterprise-specific fields to log record."""
        super().add_fields(log_record, record, message_dict)
        
        # Add timestamp in ISO format
        log_record['timestamp'] = datetime.utcnow().isoformat() + 'Z'
        
        # Add correlation IDs
        log_record['request_id'] = request_id_var.get()
        log_record['user_id'] = user_id_var.get()
        log_record['trace_id'] = trace_id_var.get()
        
        # Add service information
        log_record['service'] = 'aiml-platform'
        log_record['version'] = '2.0.0'
        log_record['environment'] = 'production'  # Should come from config
        
        # Add log metadata
        log_record['logger_name'] = record.name
        log_record['level'] = record.levelname
        log_record['module'] = record.module
        log_record['function'] = record.funcName
        log_record['line'] = record.lineno
        
        # Add thread and process info
        log_record['thread_id'] = record.thread
        log_record['thread_name'] = record.threadName
        log_record['process_id'] = record.process
        
        # Add category if present in extra
        if hasattr(record, 'category'):
            log_record['category'] = record.category
        else:
            log_record['category'] = LogCategory.APPLICATION.value


class PerformanceLogger:
    """Logger for performance metrics."""
    
    def __init__(self, logger_name: str = "aiml.performance"):
        self.logger = logging.getLogger(logger_name)
    
    def log_request_performance(
        self,
        endpoint: str,
        method: str,
        duration: float,
        status_code: int,
        request_size: Optional[int] = None,
        response_size: Optional[int] = None
    ) -> None:
        """Log request performance metrics."""
        self.logger.info(
            "Request performance metrics",
            extra={
                "category": LogCategory.PERFORMANCE.value,
                "endpoint": endpoint,
                "method": method,
                "duration_ms": round(duration * 1000, 2),
                "status_code": status_code,
                "request_size_bytes": request_size,
                "response_size_bytes": response_size
            }
        )
    
    def log_ml_performance(
        self,
        operation: str,
        model_name: str,
        duration: float,
        input_size: Optional[int] = None,
        output_size: Optional[int] = None,
        accuracy: Optional[float] = None
    ) -> None:
        """Log ML operation performance metrics."""
        self.logger.info(
            "ML operation performance metrics",
            extra={
                "category": LogCategory.ML_OPERATIONS.value,
                "operation": operation,
                "model_name": model_name,
                "duration_ms": round(duration * 1000, 2),
                "input_size": input_size,
                "output_size": output_size,
                "accuracy": accuracy
            }
        )
    
    def log_database_performance(
        self,
        query_type: str,
        table: str,
        duration: float,
        rows_affected: Optional[int] = None
    ) -> None:
        """Log database performance metrics."""
        self.logger.info(
            "Database performance metrics",
            extra={
                "category": LogCategory.PERFORMANCE.value,
                "query_type": query_type,
                "table": table,
                "duration_ms": round(duration * 1000, 2),
                "rows_affected": rows_affected
            }
        )


class SecurityLogger:
    """Logger for security events."""
    
    def __init__(self, logger_name: str = "aiml.security"):
        self.logger = logging.getLogger(logger_name)
    
    def log_authentication_attempt(
        self,
        username: str,
        success: bool,
        ip_address: str,
        user_agent: str,
        failure_reason: Optional[str] = None
    ) -> None:
        """Log authentication attempts."""
        level = logging.INFO if success else logging.WARNING
        message = "Authentication successful" if success else "Authentication failed"
        
        self.logger.log(
            level,
            message,
            extra={
                "category": LogCategory.SECURITY.value,
                "event_type": "authentication",
                "username": username,
                "success": success,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "failure_reason": failure_reason
            }
        )
    
    def log_authorization_failure(
        self,
        user_id: str,
        resource: str,
        action: str,
        ip_address: str
    ) -> None:
        """Log authorization failures."""
        self.logger.warning(
            "Authorization denied",
            extra={
                "category": LogCategory.SECURITY.value,
                "event_type": "authorization_denied",
                "user_id": user_id,
                "resource": resource,
                "action": action,
                "ip_address": ip_address
            }
        )
    
    def log_suspicious_activity(
        self,
        activity_type: str,
        description: str,
        ip_address: str,
        user_id: Optional[str] = None,
        severity: str = "medium"
    ) -> None:
        """Log suspicious activities."""
        self.logger.error(
            "Suspicious activity detected",
            extra={
                "category": LogCategory.SECURITY.value,
                "event_type": "suspicious_activity",
                "activity_type": activity_type,
                "description": description,
                "ip_address": ip_address,
                "user_id": user_id,
                "severity": severity
            }
        )


class AuditLogger:
    """Logger for audit events."""
    
    def __init__(self, logger_name: str = "aiml.audit"):
        self.logger = logging.getLogger(logger_name)
    
    def log_user_action(
        self,
        user_id: str,
        action: str,
        resource: str,
        resource_id: Optional[str] = None,
        changes: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None
    ) -> None:
        """Log user actions for audit trail."""
        self.logger.info(
            "User action performed",
            extra={
                "category": LogCategory.AUDIT.value,
                "event_type": "user_action",
                "user_id": user_id,
                "action": action,
                "resource": resource,
                "resource_id": resource_id,
                "changes": changes,
                "ip_address": ip_address
            }
        )
    
    def log_data_access(
        self,
        user_id: str,
        data_type: str,
        data_id: str,
        access_type: str,
        ip_address: Optional[str] = None
    ) -> None:
        """Log data access for compliance."""
        self.logger.info(
            "Data access logged",
            extra={
                "category": LogCategory.AUDIT.value,
                "event_type": "data_access",
                "user_id": user_id,
                "data_type": data_type,
                "data_id": data_id,
                "access_type": access_type,
                "ip_address": ip_address
            }
        )
    
    def log_model_operation(
        self,
        user_id: str,
        operation: str,
        model_id: str,
        model_name: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log ML model operations."""
        self.logger.info(
            "Model operation performed",
            extra={
                "category": LogCategory.AUDIT.value,
                "event_type": "model_operation",
                "user_id": user_id,
                "operation": operation,
                "model_id": model_id,
                "model_name": model_name,
                "parameters": parameters
            }
        )


class BusinessLogger:
    """Logger for business events."""
    
    def __init__(self, logger_name: str = "aiml.business"):
        self.logger = logging.getLogger(logger_name)
    
    def log_model_training_started(
        self,
        user_id: str,
        model_name: str,
        dataset_size: int,
        estimated_duration: Optional[int] = None
    ) -> None:
        """Log model training start."""
        self.logger.info(
            "Model training started",
            extra={
                "category": LogCategory.BUSINESS.value,
                "event_type": "training_started",
                "user_id": user_id,
                "model_name": model_name,
                "dataset_size": dataset_size,
                "estimated_duration_minutes": estimated_duration
            }
        )
    
    def log_model_training_completed(
        self,
        user_id: str,
        model_name: str,
        duration: float,
        accuracy: Optional[float] = None,
        success: bool = True
    ) -> None:
        """Log model training completion."""
        self.logger.info(
            "Model training completed",
            extra={
                "category": LogCategory.BUSINESS.value,
                "event_type": "training_completed",
                "user_id": user_id,
                "model_name": model_name,
                "duration_minutes": round(duration / 60, 2),
                "accuracy": accuracy,
                "success": success
            }
        )
    
    def log_prediction_request(
        self,
        user_id: str,
        model_name: str,
        input_size: int,
        prediction_count: int
    ) -> None:
        """Log prediction requests for billing/analytics."""
        self.logger.info(
            "Prediction request processed",
            extra={
                "category": LogCategory.BUSINESS.value,
                "event_type": "prediction_request",
                "user_id": user_id,
                "model_name": model_name,
                "input_size": input_size,
                "prediction_count": prediction_count
            }
        )


def setup_enterprise_logging(
    log_level: str = "INFO",
    log_format: str = "json",
    enable_console: bool = True,
    enable_file: bool = True,
    log_file_path: str = "/var/log/aiml-platform/app.log"
) -> None:
    """Setup enterprise logging configuration."""
    
    # Define formatters
    formatters = {
        "json": {
            "()": EnterpriseJSONFormatter,
            "format": "%(timestamp)s %(level)s %(name)s %(message)s"
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        }
    }
    
    # Define handlers
    handlers = {}
    
    if enable_console:
        handlers["console"] = {
            "class": "logging.StreamHandler",
            "level": log_level,
            "formatter": log_format,
            "stream": "ext://sys.stdout"
        }
    
    if enable_file:
        handlers["file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "level": log_level,
            "formatter": log_format,
            "filename": log_file_path,
            "maxBytes": 100 * 1024 * 1024,  # 100MB
            "backupCount": 10,
            "encoding": "utf-8"
        }
    
    # Define loggers
    loggers = {
        "aiml": {
            "level": log_level,
            "handlers": list(handlers.keys()),
            "propagate": False
        },
        "aiml.security": {
            "level": "INFO",
            "handlers": list(handlers.keys()),
            "propagate": False
        },
        "aiml.audit": {
            "level": "INFO",
            "handlers": list(handlers.keys()),
            "propagate": False
        },
        "aiml.performance": {
            "level": "INFO",
            "handlers": list(handlers.keys()),
            "propagate": False
        },
        "aiml.business": {
            "level": "INFO",
            "handlers": list(handlers.keys()),
            "propagate": False
        }
    }
    
    # Configure logging
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": formatters,
        "handlers": handlers,
        "loggers": loggers,
        "root": {
            "level": log_level,
            "handlers": list(handlers.keys())
        }
    }
    
    logging.config.dictConfig(logging_config)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance."""
    return logging.getLogger(name)


def set_correlation_id(request_id: str, user_id: Optional[str] = None, trace_id: Optional[str] = None) -> None:
    """Set correlation IDs for request tracking."""
    request_id_var.set(request_id)
    if user_id:
        user_id_var.set(user_id)
    if trace_id:
        trace_id_var.set(trace_id)


def clear_correlation_id() -> None:
    """Clear correlation IDs."""
    request_id_var.set(None)
    user_id_var.set(None)
    trace_id_var.set(None)


# Global logger instances
performance_logger = PerformanceLogger()
security_logger = SecurityLogger()
audit_logger = AuditLogger()
business_logger = BusinessLogger()
