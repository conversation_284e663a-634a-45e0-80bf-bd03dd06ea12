"""
Enterprise Input Validation and Sanitization
===========================================

Comprehensive input validation, sanitization, and security checks
for all API endpoints with enterprise-grade protection.

Author: Enterprise AI/ML Platform Team
Version: 2.0.0
"""

import re
import html
import bleach
import validators
from typing import Any, Dict, List, Optional, Union, Type
from datetime import datetime, date
from decimal import Decimal, InvalidOperation
from pydantic import BaseModel, validator, Field
from pydantic.validators import str_validator
from email_validator import validate_email, EmailNotValidError

from .enterprise_exceptions import ValidationException, ErrorDetail, ErrorCode


class ValidationConfig:
    """Configuration for validation rules."""
    
    # String validation
    MAX_STRING_LENGTH = 10000
    MAX_TEXT_LENGTH = 100000
    MAX_NAME_LENGTH = 255
    MAX_DESCRIPTION_LENGTH = 2000
    
    # Numeric validation
    MAX_INTEGER = 2**31 - 1
    MIN_INTEGER = -(2**31)
    MAX_FLOAT = 1e308
    MIN_FLOAT = -1e308
    
    # File validation
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    ALLOWED_IMAGE_TYPES = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'}
    ALLOWED_DATA_TYPES = {'csv', 'json', 'xlsx', 'parquet', 'txt'}
    ALLOWED_MODEL_TYPES = {'pkl', 'joblib', 'h5', 'pb', 'onnx', 'pt', 'pth'}
    
    # Security patterns
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
        r"(--|#|/\*|\*/)",
        r"(\bxp_cmdshell\b|\bsp_executesql\b)"
    ]
    
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe[^>]*>.*?</iframe>",
        r"<object[^>]*>.*?</object>"
    ]
    
    PATH_TRAVERSAL_PATTERNS = [
        r"\.\./",
        r"\.\.\\",
        r"%2e%2e%2f",
        r"%2e%2e%5c"
    ]


class SecurityValidator:
    """Security-focused validation methods."""
    
    @staticmethod
    def check_sql_injection(value: str) -> bool:
        """Check for SQL injection patterns."""
        for pattern in ValidationConfig.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                return True
        return False
    
    @staticmethod
    def check_xss(value: str) -> bool:
        """Check for XSS patterns."""
        for pattern in ValidationConfig.XSS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                return True
        return False
    
    @staticmethod
    def check_path_traversal(value: str) -> bool:
        """Check for path traversal patterns."""
        for pattern in ValidationConfig.PATH_TRAVERSAL_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                return True
        return False
    
    @staticmethod
    def sanitize_html(value: str) -> str:
        """Sanitize HTML content."""
        # Allow only safe HTML tags
        allowed_tags = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
        allowed_attributes = {}
        
        return bleach.clean(value, tags=allowed_tags, attributes=allowed_attributes, strip=True)
    
    @staticmethod
    def escape_html(value: str) -> str:
        """Escape HTML entities."""
        return html.escape(value)


class DataValidator:
    """Data type and format validation."""
    
    @staticmethod
    def validate_string(
        value: Any,
        min_length: int = 0,
        max_length: int = ValidationConfig.MAX_STRING_LENGTH,
        pattern: Optional[str] = None,
        allow_empty: bool = True
    ) -> str:
        """Validate string input."""
        if value is None:
            if allow_empty:
                return ""
            raise ValidationException("String value is required")
        
        if not isinstance(value, str):
            value = str(value)
        
        # Check length
        if len(value) < min_length:
            raise ValidationException(f"String must be at least {min_length} characters long")
        
        if len(value) > max_length:
            raise ValidationException(f"String must not exceed {max_length} characters")
        
        # Check pattern
        if pattern and not re.match(pattern, value):
            raise ValidationException(f"String does not match required pattern: {pattern}")
        
        # Security checks
        if SecurityValidator.check_sql_injection(value):
            raise ValidationException("String contains potential SQL injection")
        
        if SecurityValidator.check_xss(value):
            raise ValidationException("String contains potential XSS content")
        
        if SecurityValidator.check_path_traversal(value):
            raise ValidationException("String contains potential path traversal")
        
        return value.strip()
    
    @staticmethod
    def validate_email(value: str) -> str:
        """Validate email address."""
        try:
            validated_email = validate_email(value)
            return validated_email.email
        except EmailNotValidError as e:
            raise ValidationException(f"Invalid email address: {str(e)}")
    
    @staticmethod
    def validate_url(value: str) -> str:
        """Validate URL."""
        if not validators.url(value):
            raise ValidationException("Invalid URL format")
        return value
    
    @staticmethod
    def validate_integer(
        value: Any,
        min_value: int = ValidationConfig.MIN_INTEGER,
        max_value: int = ValidationConfig.MAX_INTEGER
    ) -> int:
        """Validate integer input."""
        try:
            if isinstance(value, str):
                value = int(value)
            elif isinstance(value, float):
                if value.is_integer():
                    value = int(value)
                else:
                    raise ValidationException("Value is not an integer")
            elif not isinstance(value, int):
                raise ValidationException("Value must be an integer")
            
            if value < min_value or value > max_value:
                raise ValidationException(f"Integer must be between {min_value} and {max_value}")
            
            return value
        except (ValueError, TypeError) as e:
            raise ValidationException(f"Invalid integer value: {str(e)}")
    
    @staticmethod
    def validate_float(
        value: Any,
        min_value: float = ValidationConfig.MIN_FLOAT,
        max_value: float = ValidationConfig.MAX_FLOAT
    ) -> float:
        """Validate float input."""
        try:
            if isinstance(value, str):
                value = float(value)
            elif not isinstance(value, (int, float)):
                raise ValidationException("Value must be a number")
            
            value = float(value)
            
            if value < min_value or value > max_value:
                raise ValidationException(f"Number must be between {min_value} and {max_value}")
            
            return value
        except (ValueError, TypeError) as e:
            raise ValidationException(f"Invalid number value: {str(e)}")
    
    @staticmethod
    def validate_decimal(value: Any, max_digits: int = 10, decimal_places: int = 2) -> Decimal:
        """Validate decimal input."""
        try:
            if isinstance(value, str):
                decimal_value = Decimal(value)
            elif isinstance(value, (int, float)):
                decimal_value = Decimal(str(value))
            else:
                raise ValidationException("Value must be a decimal number")
            
            # Check precision
            sign, digits, exponent = decimal_value.as_tuple()
            if len(digits) > max_digits:
                raise ValidationException(f"Decimal cannot have more than {max_digits} digits")
            
            if exponent < -decimal_places:
                raise ValidationException(f"Decimal cannot have more than {decimal_places} decimal places")
            
            return decimal_value
        except (InvalidOperation, ValueError) as e:
            raise ValidationException(f"Invalid decimal value: {str(e)}")
    
    @staticmethod
    def validate_datetime(value: Any) -> datetime:
        """Validate datetime input."""
        if isinstance(value, datetime):
            return value
        elif isinstance(value, str):
            try:
                # Try ISO format first
                return datetime.fromisoformat(value.replace('Z', '+00:00'))
            except ValueError:
                try:
                    # Try common formats
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y/%m/%d %H:%M:%S', '%Y/%m/%d']:
                        try:
                            return datetime.strptime(value, fmt)
                        except ValueError:
                            continue
                    raise ValidationException("Invalid datetime format")
                except ValueError as e:
                    raise ValidationException(f"Invalid datetime: {str(e)}")
        else:
            raise ValidationException("Datetime must be a string or datetime object")
    
    @staticmethod
    def validate_list(
        value: Any,
        item_type: Type = str,
        min_items: int = 0,
        max_items: int = 1000,
        unique: bool = False
    ) -> List[Any]:
        """Validate list input."""
        if not isinstance(value, list):
            raise ValidationException("Value must be a list")
        
        if len(value) < min_items:
            raise ValidationException(f"List must contain at least {min_items} items")
        
        if len(value) > max_items:
            raise ValidationException(f"List cannot contain more than {max_items} items")
        
        # Validate each item
        validated_items = []
        for i, item in enumerate(value):
            try:
                if item_type == str:
                    validated_item = DataValidator.validate_string(item)
                elif item_type == int:
                    validated_item = DataValidator.validate_integer(item)
                elif item_type == float:
                    validated_item = DataValidator.validate_float(item)
                else:
                    validated_item = item
                validated_items.append(validated_item)
            except ValidationException as e:
                raise ValidationException(f"Invalid item at index {i}: {e.message}")
        
        # Check uniqueness
        if unique and len(set(validated_items)) != len(validated_items):
            raise ValidationException("List items must be unique")
        
        return validated_items
    
    @staticmethod
    def validate_dict(
        value: Any,
        required_keys: Optional[List[str]] = None,
        allowed_keys: Optional[List[str]] = None,
        max_keys: int = 100
    ) -> Dict[str, Any]:
        """Validate dictionary input."""
        if not isinstance(value, dict):
            raise ValidationException("Value must be a dictionary")
        
        if len(value) > max_keys:
            raise ValidationException(f"Dictionary cannot contain more than {max_keys} keys")
        
        # Check required keys
        if required_keys:
            missing_keys = set(required_keys) - set(value.keys())
            if missing_keys:
                raise ValidationException(f"Missing required keys: {', '.join(missing_keys)}")
        
        # Check allowed keys
        if allowed_keys:
            invalid_keys = set(value.keys()) - set(allowed_keys)
            if invalid_keys:
                raise ValidationException(f"Invalid keys: {', '.join(invalid_keys)}")
        
        # Validate keys are strings
        for key in value.keys():
            if not isinstance(key, str):
                raise ValidationException("Dictionary keys must be strings")
            DataValidator.validate_string(key, max_length=100)
        
        return value


class FileValidator:
    """File validation methods."""
    
    @staticmethod
    def validate_file_size(file_size: int, max_size: int = ValidationConfig.MAX_FILE_SIZE) -> None:
        """Validate file size."""
        if file_size > max_size:
            raise ValidationException(f"File size exceeds maximum allowed size of {max_size} bytes")
    
    @staticmethod
    def validate_file_type(filename: str, allowed_types: set) -> str:
        """Validate file type by extension."""
        if '.' not in filename:
            raise ValidationException("File must have an extension")
        
        extension = filename.rsplit('.', 1)[1].lower()
        if extension not in allowed_types:
            raise ValidationException(f"File type '{extension}' not allowed. Allowed types: {', '.join(allowed_types)}")
        
        return extension
    
    @staticmethod
    def validate_image_file(filename: str, file_size: int) -> str:
        """Validate image file."""
        FileValidator.validate_file_size(file_size)
        return FileValidator.validate_file_type(filename, ValidationConfig.ALLOWED_IMAGE_TYPES)
    
    @staticmethod
    def validate_data_file(filename: str, file_size: int) -> str:
        """Validate data file."""
        FileValidator.validate_file_size(file_size)
        return FileValidator.validate_file_type(filename, ValidationConfig.ALLOWED_DATA_TYPES)
    
    @staticmethod
    def validate_model_file(filename: str, file_size: int) -> str:
        """Validate model file."""
        FileValidator.validate_file_size(file_size, max_size=500 * 1024 * 1024)  # 500MB for models
        return FileValidator.validate_file_type(filename, ValidationConfig.ALLOWED_MODEL_TYPES)


class BusinessValidator:
    """Business logic validation methods."""
    
    @staticmethod
    def validate_model_name(name: str) -> str:
        """Validate ML model name."""
        name = DataValidator.validate_string(
            name,
            min_length=1,
            max_length=ValidationConfig.MAX_NAME_LENGTH,
            pattern=r'^[a-zA-Z0-9_-]+$'
        )
        
        # Check for reserved names
        reserved_names = {'admin', 'system', 'default', 'null', 'undefined'}
        if name.lower() in reserved_names:
            raise ValidationException(f"Model name '{name}' is reserved")
        
        return name
    
    @staticmethod
    def validate_dataset_name(name: str) -> str:
        """Validate dataset name."""
        return DataValidator.validate_string(
            name,
            min_length=1,
            max_length=ValidationConfig.MAX_NAME_LENGTH,
            pattern=r'^[a-zA-Z0-9_\s-]+$'
        )
    
    @staticmethod
    def validate_user_role(role: str) -> str:
        """Validate user role."""
        allowed_roles = {'admin', 'user', 'viewer', 'data_scientist', 'ml_engineer'}
        if role not in allowed_roles:
            raise ValidationException(f"Invalid role. Allowed roles: {', '.join(allowed_roles)}")
        return role
    
    @staticmethod
    def validate_hyperparameters(params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate ML hyperparameters."""
        validated_params = DataValidator.validate_dict(
            params,
            max_keys=50
        )
        
        # Validate common hyperparameter types
        for key, value in validated_params.items():
            if key in ['learning_rate', 'dropout_rate', 'momentum']:
                validated_params[key] = DataValidator.validate_float(value, min_value=0.0, max_value=1.0)
            elif key in ['epochs', 'batch_size', 'max_depth']:
                validated_params[key] = DataValidator.validate_integer(value, min_value=1, max_value=10000)
            elif key in ['optimizer', 'activation', 'loss_function']:
                validated_params[key] = DataValidator.validate_string(value, max_length=50)
        
        return validated_params


# Pydantic validators for use in models
def validate_secure_string(cls, v):
    """Pydantic validator for secure strings."""
    return DataValidator.validate_string(v)


def validate_email_field(cls, v):
    """Pydantic validator for email fields."""
    return DataValidator.validate_email(v)


def validate_url_field(cls, v):
    """Pydantic validator for URL fields."""
    return DataValidator.validate_url(v)
